<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>YOLOv8 Object Detection</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 40px;
            background-color: #f0f2f5;
            color: #333;
        }
        h2 {
            color: #1a73e8;
        }
        .container {
            max-width: 700px;
            margin: auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        input[type="file"] {
            margin: 10px 0;
        }
        button {
            padding: 10px 20px;
            background-color: #1a73e8;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #155bb5;
        }
        .preview {
            margin-top: 20px;
        }
        img {
            max-width: 100%;
            border-radius: 5px;
        }
        .detections {
            margin-top: 10px;
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Upload an Image for Detection</h2>
        <label for="imageInput">Select image to upload:</label>
        <input type="file" id="imageInput" accept="image/*" title="Choose an image file" placeholder="Select image">
        <button onclick="uploadImage()">Detect</button>

        <div class="preview">
            <h3>Original Image</h3>
            <img id="original" src="" alt="Original Preview">
            <h3>Detected Image</h3>
            <img id="output" src="" alt="Prediction Preview">
        </div>

        <div class="detections" id="detections"></div>
    </div>

    <script>
        function uploadImage() {
            const input = document.getElementById('imageInput');
            const file = input.files[0];
            if (!file) return alert("Please select an image.");

            const formData = new FormData();
            formData.append('image', file);

            document.getElementById('original').src = URL.createObjectURL(file);

            fetch('http://localhost:5000/detect', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('output').src = data.predicted_image;
                const det = document.getElementById('detections');
                det.innerHTML = '<h3>Detections:</h3>' + data.detections.map(d => `<div>${d.label} - ${d.confidence}</div>`).join('');
            })
            .catch(err => {
                console.error(err);
                alert("Error detecting objects.");
            });
        }
    </script>
</body>
</html>
